{% comment %}
  Featured Products Section
  This section displays a grid of featured products with add to cart functionality
{% endcomment %}

<section class="featured-products">
  <div class="container">
    {% if section.settings.title != blank %}
      <h2 class="featured-products__title">{{ section.settings.title }}</h2>
    {% endif %}
    
    <div class="product-grid">
      {% if section.settings.collection != blank %}
        {% assign featured_collection = collections[section.settings.collection] %}
        {% for product in featured_collection.products limit: section.settings.products_to_show %}
          {% render 'product-card', product: product %}
        {% endfor %}
      {% else %}
        {% comment %} Fallback to manual product selection {% endcomment %}
        {% for block in section.blocks %}
          {% case block.type %}
            {% when 'product' %}
              {% if block.settings.product != blank %}
                {% render 'product-card', product: block.settings.product %}
              {% endif %}
          {% endcase %}
        {% endfor %}
      {% endif %}
    </div>
  </div>
</section>

{% schema %}
{
  "name": "Featured Products",
  "tag": "section",
  "class": "section-featured-products",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Featured Products"
    },
    {
      "type": "collection",
      "id": "collection",
      "label": "Collection",
      "info": "Select a collection to display products from"
    },
    {
      "type": "range",
      "id": "products_to_show",
      "label": "Number of products to show",
      "min": 2,
      "max": 12,
      "step": 1,
      "default": 4
    },
    {
      "type": "header",
      "content": "Section Spacing"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "Padding Top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "Padding Bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "default": 40
    }
  ],
  "blocks": [
    {
      "type": "product",
      "name": "Product",
      "settings": [
        {
          "type": "product",
          "id": "product",
          "label": "Product"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Featured Products",
      "blocks": [
        {
          "type": "product"
        },
        {
          "type": "product"
        },
        {
          "type": "product"
        },
        {
          "type": "product"
        }
      ]
    }
  ]
}
{% endschema %}

<style>
  .section-featured-products {
    padding-top: {{ section.settings.padding_top }}px;
    padding-bottom: {{ section.settings.padding_bottom }}px;
  }
</style>
