/* Enhanced Product Card Styles - Buy Buttons (Add to Cart & Buy Now) */

/* Ensure card content is properly structured for bottom buttons */
.card-wrapper.product-card-wrapper {
  height: 100%;
}

.card-wrapper.product-card-wrapper .card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-wrapper.product-card-wrapper .card__content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.card-wrapper.product-card-wrapper .card__information {
  flex-grow: 1;
}

/* Buy Buttons Container */
.card__buy-buttons {
  margin-top: auto;
  padding-top: 12px;
}

/* Button Group Layout */
.card__button-group {
  display: flex;
  gap: 8px;
  width: 100%;
}

.card__button-group .button {
  flex: 1;
  min-height: 44px;
}

/* Enhanced Buy Button Styling */
.card__add-to-cart-btn,
.card__buy-now-btn,
.card__choose-options-btn,
.card__sold-out-btn {
  padding: 12px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  text-decoration: none;
}

/* Add to Cart Button (Secondary style) */
.card__add-to-cart-btn.button--secondary {
  background: #fff;
  color: #007bff;
  border: 2px solid #007bff;
}

.card__add-to-cart-btn.button--secondary:hover:not(:disabled) {
  background: #007bff;
  color: #fff;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

/* Buy Now Button (Primary style) */
.card__buy-now-btn.button--primary {
  background: #28a745;
  color: #fff;
  border: 2px solid #28a745;
}

.card__buy-now-btn.button--primary:hover:not(:disabled) {
  background: #218838;
  border-color: #218838;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

/* Choose Options Button */
.card__choose-options-btn.button--primary {
  background: #007bff;
  color: #fff;
  border: 2px solid #007bff;
}

.card__choose-options-btn.button--primary:hover:not(:disabled) {
  background: #0056b3;
  border-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

/* Sold Out Button */
.card__sold-out-btn.button--secondary:disabled {
  background: #6c757d;
  color: #fff;
  border: 2px solid #6c757d;
  cursor: not-allowed;
  opacity: 0.8;
}

/* Loading state */
.card__add-to-cart-btn:disabled {
  cursor: not-allowed;
}

.card__add-to-cart-btn .loading-spinner {
  display: none;
}

.card__add-to-cart-btn.loading .add-to-cart-text {
  opacity: 0;
}

.card__add-to-cart-btn.loading .loading-spinner {
  display: block;
  position: absolute;
}

/* Success state animation */
.card__add-to-cart-btn.success {
  background: #28a745;
  border-color: #28a745;
}

.card__add-to-cart-btn.success .add-to-cart-text::after {
  content: " ✓";
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card__add-to-cart-btn {
    padding: 10px 14px;
    font-size: 13px;
    min-height: 40px;
  }
  
  .card__add-to-cart {
    padding-top: 8px;
  }
}

/* Hide original quick-add when our enhanced button is present */
.card-wrapper.product-card-wrapper .card__add-to-cart + .quick-add {
  display: none;
}

/* Ensure proper spacing in card information */
.card__information .card-information {
  margin-bottom: 8px;
}

/* Price styling improvements */
.card__information .price {
  margin-bottom: 8px;
  font-weight: 600;
}

.card__information .price .price__current {
  color: #333;
  font-size: 16px;
}

.card__information .price--on-sale .price__current {
  color: #e74c3c;
}

.card__information .price .price__compare {
  color: #999;
  text-decoration: line-through;
  font-size: 14px;
  margin-left: 8px;
}

/* Badge positioning improvements */
.card__badge {
  z-index: 2;
}

.badge {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Card hover effects */
.card-wrapper.product-card-wrapper:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease;
}

.card-wrapper.product-card-wrapper:hover .card {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* Image hover effects */
.card__media .media img {
  transition: transform 0.3s ease;
}

.card-wrapper.product-card-wrapper:hover .card__media .media img {
  transform: scale(1.02);
}

/* Grid layout improvements for product cards */
.collection .product-grid,
.featured-collection .product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
}

@media (max-width: 768px) {
  .collection .product-grid,
  .featured-collection .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
  }
}

/* Ensure consistent card heights in grid */
.product-grid .card-wrapper {
  height: 100%;
}

/* Loading spinner animation */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-spinner svg {
  animation: spin 1s linear infinite;
  width: 16px;
  height: 16px;
}

/* Accessibility improvements */
.card__add-to-cart-btn:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.card__add-to-cart-btn:focus:not(:focus-visible) {
  outline: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card__add-to-cart-btn.button--primary {
    border: 3px solid;
  }
  
  .card__add-to-cart-btn.button--secondary:disabled {
    border: 3px solid;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .card-wrapper.product-card-wrapper:hover,
  .card__add-to-cart-btn,
  .card__buy-now-btn,
  .card__media .media img {
    transition: none;
    transform: none;
  }
}

/* ===== CART DRAWER STYLES ===== */

/* Cart Drawer Container */
.cart-drawer {
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.cart-drawer--open {
  pointer-events: all;
  opacity: 1;
}

/* Cart Drawer Overlay */
.cart-drawer__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  cursor: pointer;
}

/* Cart Drawer Content */
.cart-drawer__content {
  position: absolute;
  top: 0;
  right: 0;
  width: 400px;
  max-width: 90vw;
  height: 100%;
  background: #fff;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.cart-drawer--open .cart-drawer__content {
  transform: translateX(0);
}

/* Cart Drawer Header */
.cart-drawer__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #e5e5e5;
  background: #f8f9fa;
}

.cart-drawer__title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.cart-drawer__count {
  background: #007bff;
  color: #fff;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  margin-left: 8px;
}

.cart-drawer__close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.cart-drawer__close:hover {
  background: rgba(0, 0, 0, 0.1);
}

.cart-drawer__close svg {
  width: 20px;
  height: 20px;
}

/* Cart Drawer Body */
.cart-drawer__body {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* Empty Cart State */
.cart-drawer__empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px 20px;
}

.cart-drawer__empty-content {
  text-align: center;
}

.cart-drawer__empty-content h3 {
  margin: 0 0 12px 0;
  font-size: 20px;
  color: #333;
}

.cart-drawer__empty-content p {
  margin: 0 0 24px 0;
  color: #666;
}

/* Cart Items */
.cart-drawer__items {
  padding: 0;
}

.cart-drawer__item {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-bottom: 1px solid #e5e5e5;
}

.cart-drawer__item:last-child {
  border-bottom: none;
}

/* Cart Item Image */
.cart-drawer__item-image {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  background: #f5f5f5;
}

.cart-drawer__item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cart-drawer__item-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cart-drawer__item-image-placeholder svg {
  width: 40px;
  height: 40px;
  opacity: 0.3;
}

/* Cart Item Details */
.cart-drawer__item-details {
  flex: 1;
  min-width: 0;
}

.cart-drawer__item-title {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
}

.cart-drawer__item-title a {
  color: #333;
  text-decoration: none;
}

.cart-drawer__item-title a:hover {
  color: #007bff;
}

.cart-drawer__item-variant {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.cart-drawer__item-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.cart-drawer__item-price-original {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.cart-drawer__item-price-final {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

/* Cart Item Quantity */
.cart-drawer__item-quantity {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.quantity-input {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.quantity-input__button {
  background: #f8f9fa;
  border: none;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 16px;
  font-weight: 600;
}

.quantity-input__button:hover {
  background: #e9ecef;
}

.quantity-input__button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-input__input {
  border: none;
  width: 40px;
  height: 32px;
  text-align: center;
  font-size: 14px;
  background: #fff;
}

.quantity-input__input:focus {
  outline: none;
}

.cart-drawer__item-remove {
  background: none;
  border: none;
  color: #dc3545;
  font-size: 12px;
  cursor: pointer;
  text-decoration: underline;
  padding: 4px 0;
}

.cart-drawer__item-remove:hover {
  color: #c82333;
}

/* Cart Drawer Footer */
.cart-drawer__footer {
  border-top: 1px solid #e5e5e5;
  padding: 20px;
  background: #f8f9fa;
}

.cart-drawer__subtotal {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
}

.cart-drawer__subtotal-price {
  color: #333;
}

.cart-drawer__note {
  margin-bottom: 16px;
}

.cart-drawer__note label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
}

.cart-drawer__note textarea {
  width: 100%;
  min-height: 60px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  resize: vertical;
}

.cart-drawer__note textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.cart-drawer__actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.cart-drawer__continue-shopping,
.cart-drawer__view-cart,
.cart-drawer__checkout {
  width: 100%;
  padding: 12px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  text-align: center;
  border: 2px solid;
}

.cart-drawer__continue-shopping.button--secondary {
  background: #fff;
  color: #6c757d;
  border-color: #6c757d;
}

.cart-drawer__continue-shopping.button--secondary:hover {
  background: #6c757d;
  color: #fff;
}

.cart-drawer__view-cart.button--tertiary {
  background: transparent;
  color: #007bff;
  border-color: #007bff;
}

.cart-drawer__view-cart.button--tertiary:hover {
  background: #007bff;
  color: #fff;
}

.cart-drawer__checkout.button--primary {
  background: #28a745;
  color: #fff;
  border-color: #28a745;
}

.cart-drawer__checkout.button--primary:hover {
  background: #218838;
  border-color: #218838;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

/* Body scroll lock when drawer is open */
body.cart-drawer-open {
  overflow: hidden;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .cart-drawer__content {
    width: 100%;
    max-width: 100vw;
  }

  .cart-drawer__item {
    padding: 16px;
  }

  .cart-drawer__item-image {
    width: 60px;
    height: 60px;
  }

  .cart-drawer__header {
    padding: 16px;
  }

  .cart-drawer__footer {
    padding: 16px;
  }
}
