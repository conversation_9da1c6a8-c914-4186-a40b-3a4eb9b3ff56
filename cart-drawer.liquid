{% comment %}
  Cart Drawer/Sidebar Component
  Shows when items are added to cart, allows customers to continue shopping
  
  Usage: Include this in theme.liquid before closing </body> tag
  {% render 'cart-drawer' %}
{% endcomment %}

<cart-drawer class="cart-drawer" id="cart-drawer">
  <div class="cart-drawer__overlay" data-cart-drawer-close></div>
  
  <div class="cart-drawer__content">
    <div class="cart-drawer__header">
      <h2 class="cart-drawer__title">
        {{ 'sections.cart.title' | t }}
        <span class="cart-drawer__count" data-cart-count>{{ cart.item_count }}</span>
      </h2>
      <button 
        class="cart-drawer__close" 
        data-cart-drawer-close
        aria-label="{{ 'accessibility.close' | t }}"
      >
        {{- 'icon-close.svg' | inline_asset_content -}}
      </button>
    </div>

    <div class="cart-drawer__body">
      {%- if cart.item_count == 0 -%}
        <div class="cart-drawer__empty">
          <div class="cart-drawer__empty-content">
            <h3>{{ 'sections.cart.empty' | t }}</h3>
            <p>{{ 'sections.cart.empty_text' | t }}</p>
            <a href="{{ routes.all_products_collection_url }}" class="button button--primary">
              {{ 'general.continue_shopping' | t }}
            </a>
          </div>
        </div>
      {%- else -%}
        <div class="cart-drawer__items" data-cart-items>
          {%- for item in cart.items -%}
            <div class="cart-drawer__item" data-cart-item="{{ item.key }}">
              <div class="cart-drawer__item-image">
                {%- if item.image -%}
                  <img 
                    src="{{ item.image | image_url: width: 80 }}" 
                    alt="{{ item.image.alt | escape }}"
                    width="80"
                    height="80"
                    loading="lazy"
                  >
                {%- else -%}
                  <div class="cart-drawer__item-image-placeholder">
                    {{ 'product-1' | placeholder_svg_tag }}
                  </div>
                {%- endif -%}
              </div>
              
              <div class="cart-drawer__item-details">
                <h4 class="cart-drawer__item-title">
                  <a href="{{ item.url }}">{{ item.product.title | escape }}</a>
                </h4>
                
                {%- if item.variant.title != 'Default Title' -%}
                  <div class="cart-drawer__item-variant">
                    {{ item.variant.title | escape }}
                  </div>
                {%- endif -%}
                
                <div class="cart-drawer__item-price">
                  {%- if item.original_price != item.final_price -%}
                    <span class="cart-drawer__item-price-original">
                      {{ item.original_price | money }}
                    </span>
                  {%- endif -%}
                  <span class="cart-drawer__item-price-final">
                    {{ item.final_price | money }}
                  </span>
                </div>
              </div>
              
              <div class="cart-drawer__item-quantity">
                <div class="quantity-input">
                  <button 
                    class="quantity-input__button quantity-input__button--minus"
                    data-quantity-change="{{ item.key }}"
                    data-quantity-value="{{ item.quantity | minus: 1 }}"
                    aria-label="{{ 'products.product.quantity.decrease' | t: product: item.product.title | escape }}"
                  >
                    <span>−</span>
                  </button>
                  <input 
                    class="quantity-input__input"
                    type="number"
                    value="{{ item.quantity }}"
                    min="0"
                    data-quantity-input="{{ item.key }}"
                    aria-label="{{ 'products.product.quantity.input_label' | t: product: item.product.title | escape }}"
                  >
                  <button 
                    class="quantity-input__button quantity-input__button--plus"
                    data-quantity-change="{{ item.key }}"
                    data-quantity-value="{{ item.quantity | plus: 1 }}"
                    aria-label="{{ 'products.product.quantity.increase' | t: product: item.product.title | escape }}"
                  >
                    <span>+</span>
                  </button>
                </div>
                
                <button 
                  class="cart-drawer__item-remove"
                  data-cart-remove="{{ item.key }}"
                  aria-label="{{ 'sections.cart.remove_title' | t: title: item.product.title | escape }}"
                >
                  {{ 'sections.cart.remove' | t }}
                </button>
              </div>
            </div>
          {%- endfor -%}
        </div>
      {%- endif -%}
    </div>

    {%- if cart.item_count > 0 -%}
      <div class="cart-drawer__footer">
        <div class="cart-drawer__subtotal">
          <div class="cart-drawer__subtotal-label">
            {{ 'sections.cart.subtotal' | t }}
          </div>
          <div class="cart-drawer__subtotal-price" data-cart-total>
            {{ cart.total_price | money_with_currency }}
          </div>
        </div>
        
        {%- if settings.show_cart_note -%}
          <div class="cart-drawer__note">
            <label for="cart-note">{{ 'sections.cart.note' | t }}</label>
            <textarea 
              id="cart-note" 
              name="note" 
              placeholder="{{ 'sections.cart.note_placeholder' | t }}"
              data-cart-note
            >{{ cart.note }}</textarea>
          </div>
        {%- endif -%}
        
        <div class="cart-drawer__actions">
          <button 
            class="cart-drawer__continue-shopping button button--secondary"
            data-cart-drawer-close
          >
            {{ 'general.continue_shopping' | t }}
          </button>
          
          <a 
            href="{{ routes.cart_url }}" 
            class="cart-drawer__view-cart button button--tertiary"
          >
            {{ 'sections.cart.view_cart' | t }}
          </a>
          
          <button 
            class="cart-drawer__checkout button button--primary"
            data-cart-checkout
          >
            {{ 'sections.cart.checkout' | t }}
          </button>
        </div>
      </div>
    {%- endif -%}
  </div>
</cart-drawer>

<script>
class CartDrawer extends HTMLElement {
  constructor() {
    super();
    this.addEventListener('click', this.onClick.bind(this));
    this.addEventListener('change', this.onChange.bind(this));
    
    // Listen for cart updates
    document.addEventListener('cart:updated', this.onCartUpdate.bind(this));
    document.addEventListener('cart:item-added', this.onItemAdded.bind(this));
  }

  onClick(event) {
    // Close drawer
    if (event.target.matches('[data-cart-drawer-close]')) {
      this.close();
    }
    
    // Quantity change
    if (event.target.matches('[data-quantity-change]')) {
      event.preventDefault();
      const key = event.target.dataset.quantityChange;
      const quantity = parseInt(event.target.dataset.quantityValue);
      this.updateQuantity(key, quantity);
    }
    
    // Remove item
    if (event.target.matches('[data-cart-remove]')) {
      event.preventDefault();
      const key = event.target.dataset.cartRemove;
      this.removeItem(key);
    }
    
    // Checkout
    if (event.target.matches('[data-cart-checkout]')) {
      event.preventDefault();
      window.location.href = '/checkout';
    }
  }

  onChange(event) {
    // Quantity input change
    if (event.target.matches('[data-quantity-input]')) {
      const key = event.target.dataset.quantityInput;
      const quantity = parseInt(event.target.value);
      if (quantity >= 0) {
        this.updateQuantity(key, quantity);
      }
    }
    
    // Cart note change
    if (event.target.matches('[data-cart-note]')) {
      this.updateNote(event.target.value);
    }
  }

  onCartUpdate(event) {
    this.refreshDrawer();
  }

  onItemAdded(event) {
    this.open();
    this.refreshDrawer();
  }

  open() {
    this.classList.add('cart-drawer--open');
    document.body.classList.add('cart-drawer-open');
    
    // Focus management
    this.querySelector('[data-cart-drawer-close]')?.focus();
  }

  close() {
    this.classList.remove('cart-drawer--open');
    document.body.classList.remove('cart-drawer-open');
  }

  async updateQuantity(key, quantity) {
    try {
      const response = await fetch('/cart/change.js', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: key,
          quantity: quantity
        })
      });
      
      if (response.ok) {
        const cart = await response.json();
        document.dispatchEvent(new CustomEvent('cart:updated', { detail: { cart } }));
      }
    } catch (error) {
      console.error('Error updating quantity:', error);
    }
  }

  async removeItem(key) {
    this.updateQuantity(key, 0);
  }

  async updateNote(note) {
    try {
      await fetch('/cart/update.js', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          note: note
        })
      });
    } catch (error) {
      console.error('Error updating note:', error);
    }
  }

  async refreshDrawer() {
    try {
      const response = await fetch('/?section_id=cart-drawer');
      if (response.ok) {
        const html = await response.text();
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const newContent = doc.querySelector('cart-drawer .cart-drawer__content');
        
        if (newContent) {
          this.querySelector('.cart-drawer__content').innerHTML = newContent.innerHTML;
        }
      }
    } catch (error) {
      console.error('Error refreshing cart drawer:', error);
      // Fallback: reload page
      window.location.reload();
    }
  }
}

customElements.define('cart-drawer', CartDrawer);
</script>
