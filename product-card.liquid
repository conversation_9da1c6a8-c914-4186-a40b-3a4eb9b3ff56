{% comment %}
  Product Card Component with Add to Cart Button
  Usage: {% render 'product-card', product: product %}
{% endcomment %}

<div class="product-card" data-product-id="{{ product.id }}">
  <div class="product-card__image">
    <a href="{{ product.url }}" class="product-card__image-link">
      {% if product.featured_media %}
        <img 
          src="{{ product.featured_media | image_url: width: 300 }}"
          alt="{{ product.featured_media.alt | escape }}"
          class="product-card__img"
          loading="lazy"
          width="300"
          height="300"
        >
      {% else %}
        <div class="product-card__placeholder">
          {{ 'product-1' | placeholder_svg_tag: 'placeholder-svg' }}
        </div>
      {% endif %}
    </a>
    
    {% comment %} Product badges {% endcomment %}
    <div class="product-card__badges">
      {% if product.available == false %}
        <span class="badge badge--sold-out">{{ 'products.product.sold_out' | t }}</span>
      {% elsif product.compare_at_price > product.price %}
        {% assign discount_percentage = product.compare_at_price | minus: product.price | times: 100 | divided_by: product.compare_at_price %}
        <span class="badge badge--sale">{{ 'products.product.save_percent' | t: percentage: discount_percentage | round }}</span>
      {% endif %}
    </div>
  </div>

  <div class="product-card__content">
    <h3 class="product-card__title">
      <a href="{{ product.url }}" class="product-card__title-link">
        {{ product.title | escape }}
      </a>
    </h3>

    {% comment %} Product price {% endcomment %}
    <div class="product-card__price">
      {% if product.compare_at_price > product.price %}
        <span class="price price--on-sale">
          <span class="price__current">{{ product.price | money }}</span>
          <span class="price__compare">{{ product.compare_at_price | money }}</span>
        </span>
      {% else %}
        <span class="price">
          <span class="price__current">{{ product.price | money }}</span>
        </span>
      {% endif %}
    </div>

    {% comment %} Variant selector for products with variants {% endcomment %}
    {% if product.variants.size > 1 %}
      <div class="product-card__variants">
        <select class="product-card__variant-select" data-product-select>
          {% for variant in product.variants %}
            <option 
              value="{{ variant.id }}" 
              {% unless variant.available %}disabled{% endunless %}
              data-price="{{ variant.price }}"
              data-compare-price="{{ variant.compare_at_price }}"
            >
              {{ variant.title }}
              {% unless variant.available %} - {{ 'products.product.sold_out' | t }}{% endunless %}
            </option>
          {% endfor %}
        </select>
      </div>
    {% endif %}

    {% comment %} Add to Cart Button {% endcomment %}
    <div class="product-card__actions">
      {% if product.available %}
        <form action="/cart/add" method="post" enctype="multipart/form-data" class="product-card__form">
          {% if product.variants.size == 1 %}
            <input type="hidden" name="id" value="{{ product.first_available_variant.id }}">
          {% else %}
            <input type="hidden" name="id" value="{{ product.first_available_variant.id }}" data-variant-id>
          {% endif %}
          
          <input type="hidden" name="quantity" value="1">
          
          <button 
            type="submit" 
            class="btn btn--add-to-cart"
            {% unless product.first_available_variant.available %}disabled{% endunless %}
          >
            <span class="btn__text">{{ 'products.product.add_to_cart' | t }}</span>
            <span class="btn__loading hidden">
              <svg class="spinner" width="16" height="16" viewBox="0 0 16 16">
                <circle cx="8" cy="8" r="7" stroke="currentColor" stroke-width="2" fill="none" stroke-dasharray="44" stroke-dashoffset="44">
                  <animate attributeName="stroke-dashoffset" dur="1s" values="44;0" repeatCount="indefinite"/>
                </circle>
              </svg>
            </span>
          </button>
        </form>
      {% else %}
        <button class="btn btn--sold-out" disabled>
          {{ 'products.product.sold_out' | t }}
        </button>
      {% endif %}
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Handle variant selection
  const variantSelects = document.querySelectorAll('[data-product-select]');
  
  variantSelects.forEach(select => {
    select.addEventListener('change', function() {
      const selectedOption = this.options[this.selectedIndex];
      const productCard = this.closest('.product-card');
      const variantIdInput = productCard.querySelector('[data-variant-id]');
      const addToCartBtn = productCard.querySelector('.btn--add-to-cart');
      const priceElement = productCard.querySelector('.price__current');
      const comparePriceElement = productCard.querySelector('.price__compare');
      
      // Update hidden variant ID
      if (variantIdInput) {
        variantIdInput.value = selectedOption.value;
      }
      
      // Update button state
      if (selectedOption.disabled) {
        addToCartBtn.disabled = true;
        addToCartBtn.querySelector('.btn__text').textContent = '{{ "products.product.sold_out" | t }}';
      } else {
        addToCartBtn.disabled = false;
        addToCartBtn.querySelector('.btn__text').textContent = '{{ "products.product.add_to_cart" | t }}';
      }
      
      // Update price
      const newPrice = selectedOption.dataset.price;
      const newComparePrice = selectedOption.dataset.comparePrice;
      
      if (priceElement && newPrice) {
        priceElement.textContent = new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: '{{ cart.currency.iso_code }}'
        }).format(newPrice / 100);
      }
      
      if (comparePriceElement && newComparePrice) {
        comparePriceElement.textContent = new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: '{{ cart.currency.iso_code }}'
        }).format(newComparePrice / 100);
      }
    });
  });
  
  // Handle add to cart form submission
  const addToCartForms = document.querySelectorAll('.product-card__form');
  
  addToCartForms.forEach(form => {
    form.addEventListener('submit', function(e) {
      e.preventDefault();
      
      const button = this.querySelector('.btn--add-to-cart');
      const buttonText = button.querySelector('.btn__text');
      const buttonLoading = button.querySelector('.btn__loading');
      
      // Show loading state
      button.disabled = true;
      buttonText.classList.add('hidden');
      buttonLoading.classList.remove('hidden');
      
      // Submit form data
      fetch('/cart/add.js', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: this.querySelector('[name="id"]').value,
          quantity: this.querySelector('[name="quantity"]').value
        })
      })
      .then(response => response.json())
      .then(data => {
        // Success - update cart count or show notification
        console.log('Product added to cart:', data);
        
        // You can dispatch a custom event here to update cart drawer/count
        document.dispatchEvent(new CustomEvent('cart:updated', { detail: data }));
        
        // Reset button state
        button.disabled = false;
        buttonText.classList.remove('hidden');
        buttonLoading.classList.add('hidden');
        
        // Optional: Show success message
        buttonText.textContent = '{{ "products.product.added_to_cart" | t }}';
        setTimeout(() => {
          buttonText.textContent = '{{ "products.product.add_to_cart" | t }}';
        }, 2000);
      })
      .catch(error => {
        console.error('Error adding to cart:', error);
        
        // Reset button state
        button.disabled = false;
        buttonText.classList.remove('hidden');
        buttonLoading.classList.add('hidden');
        
        // Show error message
        buttonText.textContent = '{{ "products.product.error" | t }}';
        setTimeout(() => {
          buttonText.textContent = '{{ "products.product.add_to_cart" | t }}';
        }, 2000);
      });
    });
  });
});
</script>
