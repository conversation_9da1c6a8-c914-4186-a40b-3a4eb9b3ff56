# Enhanced Product Card Implementation Guide

## Overview
This implementation adds both **Add to Cart** and **Buy Now** buttons to your Shopify product cards, along with a sliding cart drawer that appears when items are added to the cart.

## Files Modified/Created

### 1. Modified Files
- `card-product.liquid` - Enhanced with dual buy buttons

### 2. New Files Created
- `cart-drawer.liquid` - Sliding cart sidebar component
- `card-product-enhanced.css` - Styling for buttons and cart drawer
- `card-product-enhanced.js` - JavaScript functionality

## Implementation Steps

### Step 1: Include CSS in your theme
Add this line to your `theme.liquid` file in the `<head>` section:

```liquid
{{ 'card-product-enhanced.css' | asset_url | stylesheet_tag }}
```

### Step 2: Include JavaScript
Add this line before the closing `</body>` tag in your `theme.liquid`:

```liquid
{{ 'card-product-enhanced.js' | asset_url | script_tag }}
```

### Step 3: Add Cart Drawer to Theme
Add this line before the closing `</body>` tag in your `theme.liquid` (after the JavaScript):

```liquid
{% render 'cart-drawer' %}
```

### Step 4: Upload Files to Theme
Upload these files to your theme's assets folder:
- `card-product-enhanced.css`
- `card-product-enhanced.js`

Upload this file to your theme's snippets folder:
- `cart-drawer.liquid`

## Features Included

### ✅ Dual Button System
- **Add to Cart** (Blue/Secondary) - Adds item and opens cart drawer
- **Buy Now** (Green/Primary) - Adds item and redirects to checkout

### ✅ Cart Drawer Features
- Slides in from right when items are added
- Shows all cart items with images
- Quantity adjustment controls
- Remove item functionality
- Cart note field
- Continue shopping, view cart, and checkout buttons
- Real-time cart total updates

### ✅ Smart Product Handling
- Single variant products: Shows both buttons
- Multiple variant products: Shows "Choose Options" button
- Sold out products: Shows disabled "Sold Out" button

### ✅ Enhanced User Experience
- Loading states with spinners
- Success/error feedback
- Smooth animations and transitions
- Mobile responsive design
- Accessibility compliant

## Customization Options

### Button Colors
Edit the CSS variables in `card-product-enhanced.css`:

```css
/* Add to Cart Button */
.card__add-to-cart-btn.button--secondary {
  background: #fff;
  color: #007bff;
  border: 2px solid #007bff;
}

/* Buy Now Button */
.card__buy-now-btn.button--primary {
  background: #28a745;
  color: #fff;
  border: 2px solid #28a745;
}
```

### Cart Drawer Width
Modify the width in `card-product-enhanced.css`:

```css
.cart-drawer__content {
  width: 400px; /* Change this value */
  max-width: 90vw;
}
```

### Button Text
The buttons use Shopify's translation system. You can customize text in your theme's language files:

```json
{
  "products": {
    "product": {
      "add_to_cart": "Add to Cart",
      "buy_it_now": "Buy Now",
      "choose_options": "Choose Options"
    }
  }
}
```

## Usage in Templates

### Collection Pages
```liquid
{% for product in collection.products %}
  {% render 'card-product', 
    card_product: product, 
    show_vendor: section.settings.show_vendor,
    section_id: section.id 
  %}
{% endfor %}
```

### Featured Products Section
```liquid
{% for product in collections.featured.products limit: 8 %}
  {% render 'card-product', 
    card_product: product,
    section_id: section.id
  %}
{% endfor %}
```

## Browser Support
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance Notes
- CSS and JS files are minified for production
- Images in cart drawer are lazy-loaded
- Cart updates use AJAX to avoid page reloads
- Smooth animations with hardware acceleration

## Troubleshooting

### Cart Drawer Not Opening
1. Check that `cart-drawer.liquid` is in the snippets folder
2. Ensure the render tag is added to `theme.liquid`
3. Verify JavaScript file is loading without errors

### Buttons Not Working
1. Check browser console for JavaScript errors
2. Ensure CSS file is loading properly
3. Verify product has available variants

### Styling Issues
1. Check for CSS conflicts with existing theme styles
2. Ensure CSS file is loaded after theme's main CSS
3. Use browser dev tools to inspect element styles

## Support
For customization help or issues, refer to Shopify's theme development documentation or consult with a Shopify developer.
