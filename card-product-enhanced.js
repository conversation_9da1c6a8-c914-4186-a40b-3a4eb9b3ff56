/**
 * Enhanced Product Card Buy Buttons Functionality
 * Handles Add to Cart, Buy Now, and Cart Drawer integration
 */

document.addEventListener('DOMContentLoaded', function() {

  // Handle buy button clicks
  const buyButtonForms = document.querySelectorAll('[data-type="add-to-cart-form"]');

  buyButtonForms.forEach(form => {
    form.addEventListener('submit', function(e) {
      e.preventDefault();

      // Determine which button was clicked
      const clickedButton = document.activeElement;
      const action = clickedButton.dataset.action;

      if (action === 'add-to-cart') {
        handleAddToCart(this, clickedButton);
      } else if (action === 'buy-now') {
        handleBuyNow(this, clickedButton);
      }
    });

    // Handle individual button clicks
    const addToCartBtn = form.querySelector('[data-action="add-to-cart"]');
    const buyNowBtn = form.querySelector('[data-action="buy-now"]');

    if (addToCartBtn) {
      addToCartBtn.addEventListener('click', function(e) {
        e.preventDefault();
        handleAddToCart(form, this);
      });
    }

    if (buyNowBtn) {
      buyNowBtn.addEventListener('click', function(e) {
        e.preventDefault();
        handleBuyNow(form, this);
      });
    }
  });
  
  /**
   * Handle add to cart submission with cart drawer
   */
  function handleAddToCart(form, button) {
    const originalText = button.querySelector('.add-to-cart-text')?.textContent || button.textContent;

    // Set loading state
    setButtonState(button, 'loading');

    // Get form data
    const formData = new FormData(form);
    const variantId = formData.get('id');
    const quantity = formData.get('quantity') || 1;

    // Submit to Shopify cart API
    fetch('/cart/add.js', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        id: variantId,
        quantity: parseInt(quantity)
      })
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      // Success - show success state
      setButtonState(button, 'success');

      // Show cart drawer if enabled
      if (button.dataset.opensCartDrawer === 'true') {
        document.dispatchEvent(new CustomEvent('cart:item-added', {
          detail: { item: data }
        }));
      }

      // Update cart count
      updateCartCount();

      // Show success message briefly
      const successText = button.dataset.successText || 'Added!';
      const textElement = button.querySelector('.add-to-cart-text');
      if (textElement) {
        textElement.textContent = successText;
      }

      // Reset button after 2 seconds
      setTimeout(() => {
        setButtonState(button, 'default');
        if (textElement) {
          textElement.textContent = originalText;
        }
      }, 2000);

    })
    .catch(error => {
      console.error('Error adding to cart:', error);
      handleButtonError(button, originalText);
    });
  }

  /**
   * Handle buy now submission (add to cart then redirect to checkout)
   */
  function handleBuyNow(form, button) {
    const originalText = button.querySelector('.buy-now-text')?.textContent || button.textContent;

    // Set loading state
    setButtonState(button, 'loading');

    // Get form data
    const formData = new FormData(form);
    const variantId = formData.get('id');
    const quantity = formData.get('quantity') || 1;

    // Submit to Shopify cart API
    fetch('/cart/add.js', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        id: variantId,
        quantity: parseInt(quantity)
      })
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      // Success - redirect to checkout
      window.location.href = '/checkout';
    })
    .catch(error => {
      console.error('Error with buy now:', error);
      handleButtonError(button, originalText);
    });
  }

  /**
   * Handle button error state
   */
  function handleButtonError(button, originalText) {
    setButtonState(button, 'error');

    const errorText = button.dataset.errorText || 'Error - Try again';
    const textElement = button.querySelector('.add-to-cart-text') || button.querySelector('.buy-now-text');
    if (textElement) {
      textElement.textContent = errorText;
    }

    // Reset button after 3 seconds
    setTimeout(() => {
      setButtonState(button, 'default');
      if (textElement) {
        textElement.textContent = originalText;
      }
    }, 3000);
  }
  
  /**
   * Set button visual state
   */
  function setButtonState(button, state) {
    // Remove all state classes
    button.classList.remove('loading', 'success', 'error');
    button.disabled = false;
    
    switch(state) {
      case 'loading':
        button.classList.add('loading');
        button.disabled = true;
        break;
      case 'success':
        button.classList.add('success');
        break;
      case 'error':
        button.classList.add('error');
        break;
      case 'default':
      default:
        // Default state - no additional classes needed
        break;
    }
  }
  
  /**
   * Update cart count in header/drawer
   */
  function updateCartCount() {
    fetch('/cart.js')
      .then(response => response.json())
      .then(cart => {
        // Update cart count elements
        const cartCountElements = document.querySelectorAll('[data-cart-count]');
        cartCountElements.forEach(element => {
          element.textContent = cart.item_count;
          element.style.display = cart.item_count > 0 ? 'block' : 'none';
        });
        
        // Update cart total elements
        const cartTotalElements = document.querySelectorAll('[data-cart-total]');
        cartTotalElements.forEach(element => {
          element.textContent = Shopify.formatMoney(cart.total_price);
        });
        
        // Dispatch detailed cart updated event
        document.dispatchEvent(new CustomEvent('cart:updated', { 
          detail: { 
            cart: cart,
            item_count: cart.item_count,
            total_price: cart.total_price
          }
        }));
      })
      .catch(error => {
        console.error('Error updating cart count:', error);
      });
  }
  
  /**
   * Handle variant selection for products with multiple variants
   */
  const variantSelectors = document.querySelectorAll('.product-variant-select');
  variantSelectors.forEach(select => {
    select.addEventListener('change', function() {
      const productCard = this.closest('.card-wrapper');
      const form = productCard.querySelector('[data-type="add-to-cart-form"]');
      const variantIdInput = form?.querySelector('.product-variant-id');
      const addToCartBtn = productCard.querySelector('.card__add-to-cart-btn');
      
      if (variantIdInput && addToCartBtn) {
        const selectedOption = this.options[this.selectedIndex];
        const variantId = selectedOption.value;
        const isAvailable = !selectedOption.disabled;
        
        // Update hidden input
        variantIdInput.value = variantId;
        
        // Update button state
        if (isAvailable) {
          addToCartBtn.disabled = false;
          addToCartBtn.textContent = addToCartBtn.dataset.addText || 'Add to cart';
          addToCartBtn.classList.remove('button--secondary');
          addToCartBtn.classList.add('button--primary');
        } else {
          addToCartBtn.disabled = true;
          addToCartBtn.textContent = addToCartBtn.dataset.soldOutText || 'Sold out';
          addToCartBtn.classList.remove('button--primary');
          addToCartBtn.classList.add('button--secondary');
        }
        
        // Update price if data attributes are available
        updateVariantPrice(productCard, selectedOption);
      }
    });
  });
  
  /**
   * Update price display when variant changes
   */
  function updateVariantPrice(productCard, selectedOption) {
    const priceElement = productCard.querySelector('.price__current');
    const comparePriceElement = productCard.querySelector('.price__compare');
    
    if (priceElement && selectedOption.dataset.price) {
      const price = parseInt(selectedOption.dataset.price);
      const comparePrice = selectedOption.dataset.comparePrice ? parseInt(selectedOption.dataset.comparePrice) : null;
      
      // Format price using Shopify's money format
      if (typeof Shopify !== 'undefined' && Shopify.formatMoney) {
        priceElement.textContent = Shopify.formatMoney(price);
        
        if (comparePriceElement && comparePrice && comparePrice > price) {
          comparePriceElement.textContent = Shopify.formatMoney(comparePrice);
          comparePriceElement.style.display = 'inline';
        } else if (comparePriceElement) {
          comparePriceElement.style.display = 'none';
        }
      }
    }
  }
  
  /**
   * Initialize cart count on page load
   */
  updateCartCount();
  
});
