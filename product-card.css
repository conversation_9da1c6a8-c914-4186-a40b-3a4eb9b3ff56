/* Product Card Styles */
.product-card {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: 100%;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Product Image */
.product-card__image {
  position: relative;
  overflow: hidden;
  aspect-ratio: 1;
}

.product-card__image-link {
  display: block;
  width: 100%;
  height: 100%;
}

.product-card__img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-card__img {
  transform: scale(1.05);
}

.product-card__placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

/* Product Badges */
.product-card__badges {
  position: absolute;
  top: 12px;
  left: 12px;
  z-index: 2;
}

.badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge--sold-out {
  background: #666;
  color: #fff;
}

.badge--sale {
  background: #e74c3c;
  color: #fff;
}

/* Product Content */
.product-card__content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.product-card__title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.4;
}

.product-card__title-link {
  color: #333;
  text-decoration: none;
  transition: color 0.2s ease;
}

.product-card__title-link:hover {
  color: #007bff;
}

/* Product Price */
.product-card__price {
  margin-bottom: 12px;
}

.price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.price__current {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.price--on-sale .price__current {
  color: #e74c3c;
}

.price__compare {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
}

/* Variant Selector */
.product-card__variants {
  margin-bottom: 16px;
}

.product-card__variant-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background: #fff;
  cursor: pointer;
}

.product-card__variant-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Add to Cart Actions */
.product-card__actions {
  margin-top: auto;
}

.product-card__form {
  width: 100%;
}

.btn {
  width: 100%;
  padding: 12px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
}

.btn--add-to-cart {
  background: #007bff;
  color: #fff;
}

.btn--add-to-cart:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
}

.btn--add-to-cart:active {
  transform: translateY(0);
}

.btn--sold-out {
  background: #6c757d;
  color: #fff;
  cursor: not-allowed;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Loading Spinner */
.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.hidden {
  display: none !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-card__content {
    padding: 12px;
  }
  
  .product-card__title {
    font-size: 14px;
  }
  
  .price__current {
    font-size: 16px;
  }
  
  .btn {
    padding: 10px 14px;
    font-size: 13px;
  }
}

/* Grid Layout for Product Cards */
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  padding: 24px 0;
}

@media (max-width: 768px) {
  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
    padding: 16px 0;
  }
}

/* Featured Products Section */
.featured-products {
  padding: 40px 0;
}

.featured-products__title {
  text-align: center;
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 40px;
  color: #333;
}

@media (max-width: 768px) {
  .featured-products {
    padding: 24px 0;
  }
  
  .featured-products__title {
    font-size: 24px;
    margin-bottom: 24px;
  }
}
